#!/usr/bin/env python3
"""
邮件处理机器人主程序
"""

import os
import sys
import signal
import argparse
import time
from typing import Dict, Any, List
from loguru import logger

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.core import (
    ConfigManager, 
    EmailClient, 
    AIAnalyzer, 
    ProcessorManager, 
    TaskScheduler
)
from src.utils import setup_logger


class EmailBot:
    """邮件处理机器人主类"""
    
    def __init__(self, config_path: str = "config/config.yaml"):
        self.config_manager = ConfigManager(config_path)
        self.config = None
        self.email_client = None
        self.ai_analyzer = None
        self.processor_manager = None
        self.scheduler = None
        self.running = False
        
    def initialize(self) -> bool:
        """初始化机器人"""
        try:
            # 加载配置
            self.config = self.config_manager.load_config()
            
            # 设置日志
            setup_logger(self.config.log)
            
            # 初始化各个组件
            self.email_client = EmailClient(self.config.email)
            self.ai_analyzer = AIAnalyzer(self.config.ai)
            # 传递app_config参数给ProcessorManager（转换为字典）
            self.processor_manager = ProcessorManager(
                self.email_client,
                self.config.model_dump(),
                processors_dir="src/processors"
            )
            self.scheduler = TaskScheduler(self.config.scheduler)
            
            # 设置调度器回调
            self.scheduler.set_task_callback(self.process_emails)
            
            logger.info("邮件处理机器人初始化完成")
            return True
            
        except Exception as e:
            logger.error(f"初始化失败: {e}")
            return False
    
    def process_emails(self) -> Dict[str, Any]:
        """处理邮件的主要逻辑"""
        result = {
            "processed_count": 0,
            "error_count": 0,
            "results": []
        }
        
        try:
            logger.info("开始处理邮件")
            
            # 检查必要的组件是否初始化
            if not self.email_client:
                logger.error("邮件客户端未初始化")
                result["error_count"] += 1
                return result
                
            if not self.config:
                logger.error("配置未加载")
                result["error_count"] += 1
                return result
                
            if not self.ai_analyzer:
                logger.error("AI分析器未初始化")
                result["error_count"] += 1
                return result
                
            if not self.processor_manager:
                logger.error("处理器管理器未初始化")
                result["error_count"] += 1
                return result
            
            # 连接邮箱
            with self.email_client as client:
                # 获取邮件
                emails = client.fetch_emails(
                    limit=self.config.scheduler.max_emails_per_check,
                    unread_only=True
                )
                
                logger.info(f"获取到 {len(emails)} 封邮件")
                
                # 处理每封邮件
                for idx, email_msg in enumerate(emails):
                    email_info = {
                        "subject": "未知",
                        "sender": "未知",
                        "message_id": f"邮件#{idx+1}"
                    }
                    
                    try:
                        # 获取邮件基本信息用于日志
                        if hasattr(email_msg, "subject") and email_msg.subject:
                            email_info["subject"] = email_msg.subject
                        if hasattr(email_msg, "sender") and email_msg.sender:
                            email_info["sender"] = email_msg.sender
                        if hasattr(email_msg, "raw_message") and hasattr(email_msg.raw_message, "get"):
                            email_info["message_id"] = email_msg.raw_message.get("Message-ID", f"邮件#{idx+1}")
                            
                        logger.info(f"开始处理邮件: {email_info['subject']} (来自: {email_info['sender']})")
                        
                        # 转换为字典
                        email_data = email_msg.to_dict()
                        
                        # AI分析
                        analysis = self.ai_analyzer.analyze_email(email_data)
                        
                        # 执行处理器
                        processor_results = self.processor_manager.process_email(
                            email_data, analysis
                        )
                        
                        result["results"].append({
                            "email": {
                                "subject": email_data.get("subject", ""),
                                "sender": email_data.get("sender", ""),
                                "message_id": email_data.get("message_id", "")
                            },
                            "analysis": analysis.to_dict(),
                            "processor_results": [r.to_dict() for r in processor_results]
                        })
                        
                        result["processed_count"] += 1
                        logger.info(f"邮件处理成功: {email_info['subject']}")
                        
                    except Exception as e:
                        logger.error(f"处理邮件失败 ({email_info['subject']}): {e}")
                        result["error_count"] += 1
                        # 记录失败的邮件信息
                        result.setdefault("failed_emails", []).append(email_info)
                        
        except Exception as e:
            logger.error(f"邮件处理过程出错: {e}")
            result["error_count"] += 1
        
        logger.info(f"邮件处理完成 - 成功: {result['processed_count']}, 失败: {result['error_count']}")
        return result
    
    def start(self):
        """启动机器人"""
        if not self.initialize():
            logger.error("初始化失败，无法启动")
            return False
            
        self.running = True
        
        try:
            # 启动调度器
            if not self.scheduler:
                logger.error("调度器未初始化，无法启动")
                return False
                
            self.scheduler.start()
            
            logger.info("邮件处理机器人已启动")
            logger.info("按 Ctrl+C 停止运行")
            
            # 主循环
            while self.running:
                try:
                    time.sleep(1)
                except KeyboardInterrupt:
                    logger.info("收到停止信号")
                    break
                    
        except Exception as e:
            logger.error(f"运行时错误: {e}")
        finally:
            self.stop()
            
        return True
    
    def stop(self):
        """停止机器人"""
        logger.info("正在停止邮件处理机器人...")
        
        self.running = False
        
        # 停止调度器
        if self.scheduler:
            try:
                self.scheduler.stop()
                logger.info("调度器已停止")
            except Exception as e:
                logger.error(f"停止调度器时出错: {e}")
        
        # 断开邮件连接
        if self.email_client:
            try:
                self.email_client.disconnect()
                logger.info("邮件连接已断开")
            except Exception as e:
                logger.error(f"断开邮件连接时出错: {e}")
            
        logger.info("邮件处理机器人已停止")
    
    def run_once(self):
        """运行一次邮件处理"""
        if not self.initialize():
            logger.error("初始化失败")
            return False
            
        logger.info("执行一次性邮件处理")
        try:
            result = self.process_emails()
            logger.info("一次性处理完成")
            return result
        except Exception as e:
            logger.error(f"处理邮件时发生错误: {e}")
            return False
    
    def test_connection(self):
        """测试邮箱连接"""
        if not self.initialize():
            logger.error("初始化失败，无法测试连接")
            return False
            
        logger.info("测试邮箱连接...")
        
        if not self.email_client:
            logger.error("邮件客户端未初始化")
            return False
            
        try:
            success = self.email_client.connect()
            if success:
                logger.info("邮箱连接测试成功")
                self.email_client.disconnect()
                return True
            else:
                logger.error("邮箱连接测试失败")
                return False
        except Exception as e:
            logger.error(f"连接测试出错: {e}")
            return False
    
    def show_status(self) -> bool:
        """显示机器人状态"""
        if not self.initialize():
            logger.error("初始化失败，无法显示状态")
            return False
            
        logger.info("=== 邮件处理机器人状态 ===")
        
        # 配置信息
        if self.config:
            logger.info(f"邮箱服务器: {self.config.email.host}:{self.config.email.port}")
            logger.info(f"AI提供商: {self.config.ai.provider}")
            logger.info(f"调度器状态: {'启用' if self.config.scheduler.enable_scheduler else '禁用'}")
        else:
            logger.info("配置未加载")
        
        # 处理器状态
        if self.processor_manager:
            processor_status = self.processor_manager.get_processor_status()
            logger.info(f"已加载处理器: {len(processor_status)}个")
            for name, status in processor_status.items():
                logger.info(f"  - {name}: {'启用' if status['enabled'] else '禁用'}")
        else:
            logger.info("处理器管理器未初始化")
        
        # 调度器状态
        if self.scheduler:
            scheduler_status = self.scheduler.get_status()
            logger.info(f"调度器运行状态: {scheduler_status}")
        else:
            logger.info("调度器未初始化")
            
        return True


def setup_signal_handlers(bot: EmailBot):
    """设置信号处理器"""
    def signal_handler(signum, frame):
        logger.info(f"收到信号 {signum}")
        bot.stop()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="邮件处理机器人")
    parser.add_argument(
        "--config", 
        default="config/config.yaml",
        help="配置文件路径"
    )
    parser.add_argument(
        "--once", 
        action="store_true",
        help="只运行一次，不启动调度器"
    )
    parser.add_argument(
        "--test", 
        action="store_true",
        help="测试邮箱连接"
    )
    parser.add_argument(
        "--status", 
        action="store_true",
        help="显示状态信息"
    )
    
    args = parser.parse_args()
    
    # 创建机器人实例
    bot = EmailBot(args.config)
    
    # 设置信号处理器
    setup_signal_handlers(bot)
    
    try:
        if args.test:
            # 测试连接
            success = bot.test_connection()
            sys.exit(0 if success else 1)
        elif args.status:
            # 显示状态
            success = bot.show_status()
            if not success:
                logger.error("显示状态失败")
                sys.exit(1)
        elif args.once:
            # 运行一次
            result = bot.run_once()
            if result and isinstance(result, dict):
                print(f"处理完成: {result.get('processed_count', 0)}封邮件, 失败: {result.get('error_count', 0)}封")
            else:
                logger.error("执行失败")
                sys.exit(1)
        else:
            # 正常启动
            success = bot.start()
            if not success:
                logger.error("启动失败")
                sys.exit(1)
            
    except Exception as e:
        logger.error(f"程序异常: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()

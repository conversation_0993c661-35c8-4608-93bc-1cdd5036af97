"""
PDF处理器模块 - 处理PDF附件
功能：
1. 自动下载PDF附件
2. 自动提取文本
3. 自动分析文本
4. 自动发送回复邮件
5. 使用docling库解析PDF
"""
import os
import tempfile
import requests
from typing import Optional, Dict, Any
from loguru import logger
from ..core.ai_analyzer import AnalysisResult
from ..core.processor_manager import BaseProcessor
from src.utils.email_utils import EmailUtils
from docling.document_converter import DocumentConverter

class PdfProcessor(BaseProcessor):
    def __init__(self, name: str = "pdf_processor", config: Optional[Dict[str, Any]] = None):
        super().__init__(name, config)
        # 从配置加载启用状态
        self.enabled = self.config.get('enabled', True)
        self.temp_dir = tempfile.gettempdir()
        self.download_path = os.path.join(self.temp_dir, "pdf_attachments")
        os.makedirs(self.download_path, exist_ok=True)

    def can_process(self, email_data: Dict[str, Any], analysis: AnalysisResult) -> bool:
        """检查邮件是否包含PDF附件且分类为工作"""
        # 检查是否有PDF附件
        for att in email_data.get('attachments', []):
            content_type = att.get('content_type', '').lower()
            filename = att.get('filename', '').lower()
            
            # 检查MIME类型或文件扩展名
            if 'pdf' in content_type or filename.endswith('.pdf'):
                return True
        return False

    def download_pdf(self, url: str, filename: str) -> str:
        """下载PDF文件到临时目录"""
        filepath = os.path.join(self.download_path, filename)
        try:
            response = requests.get(url, timeout=10)
            response.raise_for_status()
            with open(filepath, 'wb') as f:
                f.write(response.content)
            logger.info(f"下载PDF成功: {filename}")
            return filepath
        except Exception as e:
            logger.error(f"下载PDF失败: {e}")
            return ""

    def extract_text(self, filepath: str) -> str:
        """使用docling提取PDF文本"""
        try:
            converter = DocumentConverter()
            result = converter.convert(filepath)
            # 根据docling文档，转换成功后直接获取文本
            text = result.document.export_to_text()
            logger.info(f"提取PDF文本成功: {os.path.basename(filepath)}")
            return text
        except Exception as e:
            logger.error(f"提取PDF文本失败: {e}")
            return ""

    def analyze_text(self, text: str) -> Dict[str, Any]:
        """分析PDF文本内容"""
        # 这里可以添加更复杂的分析逻辑
        return {
            "page_count": len(text.split('\f')),
            "word_count": len(text.split()),
            "keywords": ["关键词1", "关键词2"]  # 示例
        }

    def process(self, email_data: Dict[str, Any], analysis: AnalysisResult) -> Dict[str, Any]:
        """处理PDF附件邮件"""
        results = []
        sender = email_data.get('from', '')
        subject = email_data.get('subject', '无主题')
        
        for attachment in email_data.get('attachments', []):
            content_type = attachment.get('content_type', '').lower()
            filename = attachment.get('filename', 'unnamed.pdf').lower()
            
            # 检查是否是PDF附件
            if 'pdf' in content_type or filename.endswith('.pdf'):
                # 直接使用附件内容（不再需要下载URL）
                file_content = attachment.get('content', b'')
                
                if not file_content:
                    continue
                    
                # 保存到临时文件
                filepath = os.path.join(self.download_path, filename)
                with open(filepath, 'wb') as f:
                    f.write(file_content)
                logger.info(f"保存PDF附件: {filename}")
                    
                # 提取文本
                text = self.extract_text(filepath)
                if not text:
                    continue
                    
                # 分析文本
                analysis_result = self.analyze_text(text)
                
                # 发送回复邮件
                reply_subject = f"Re: {subject} - PDF已处理"
                reply_body = (
                    f"您的PDF文件 '{filename}' 已处理完成。\n\n"
                    f"分析结果：\n"
                    f"- 页数: {analysis_result['page_count']}\n"
                    f"- 字数: {analysis_result['word_count']}\n"
                    f"- 关键词: {', '.join(analysis_result['keywords'])}\n\n"
                    "此邮件为自动发送，请勿回复。"
                )
                
                EmailUtils.send_email(
                    to=sender,
                    subject=reply_subject,
                    body=reply_body
                )
                logger.info(f"已发送PDF处理结果给 {sender}")
                
                results.append({
                    "filename": filename,
                    "analysis": analysis_result,
                    "reply_sent": True
                })
        
        return {"pdf_processing_results": results}
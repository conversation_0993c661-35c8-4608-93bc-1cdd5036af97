"""
日志处理器 - 记录邮件处理日志
"""

import json
from datetime import datetime
from typing import Dict, Any, Optional
from loguru import logger
from ..core.processor_manager import BaseProcessor
from ..core.ai_analyzer import AnalysisResult


class LogProcessor(BaseProcessor):
    """日志处理器 - 记录所有邮件的处理信息"""
    
    def __init__(self, name: str, config: Optional[Dict[str, Any]] = None):
        super().__init__(name, config)
        self.log_file = config.get('log_file', 'logs/email_processing.log') if config else 'logs/email_processing.log'
        
    def can_process(self, email_data: Dict[str, Any], analysis: AnalysisResult) -> bool:
        """所有邮件都需要记录日志"""
        return True
    
    def process(self, email_data: Dict[str, Any], analysis: AnalysisResult) -> Dict[str, Any]:
        """记录邮件处理日志"""
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "email": {
                "subject": email_data.get('subject', ''),
                "sender": email_data.get('sender', ''),
                "message_id": email_data.get('message_id', ''),
                "date": email_data.get('date', ''),
                "attachments_count": len(email_data.get('attachments', []))
            },
            "analysis": analysis.to_dict(),
            "processor": self.name
        }
        
        # 记录到日志文件
        logger.info(f"邮件处理记录: {json.dumps(log_entry, ensure_ascii=False, indent=2)}")
        
        # 可以在这里添加更多的日志记录逻辑，比如写入数据库等
        
        return {
            "logged": True,
            "log_entry": log_entry
        }
    
    def get_priority(self) -> int:
        """日志处理器优先级最高，确保最后执行"""
        return 999
    
    def validate_config(self) -> bool:
        """验证配置"""
        return True

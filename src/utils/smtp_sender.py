"""
SMTP邮件发送器 - 负责发送邮件
"""

import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email import encoders
from email.utils import formataddr
from typing import List, Optional, Dict, Any
from loguru import logger
from ..core.config_manager import SMTPConfig


class SMTPSender:
    """SMTP邮件发送器"""
    
    def __init__(self, config: SMTPConfig):
        self.config = config
        
    def send_email(self, 
                   to: str, 
                   subject: str, 
                   body: str, 
                   cc: Optional[List[str]] = None,
                   bcc: Optional[List[str]] = None,
                   attachments: Optional[List[Dict[str, Any]]] = None,
                   is_html: bool = False) -> bool:
        """
        发送邮件
        
        Args:
            to: 收件人邮箱
            subject: 邮件主题
            body: 邮件正文
            cc: 抄送列表
            bcc: 密送列表
            attachments: 附件列表
            is_html: 是否为HTML格式
            
        Returns:
            bool: 发送是否成功
        """
        try:
            # 创建邮件对象
            msg = MIMEMultipart()
            
            # 设置邮件头
            msg['From'] = formataddr((self.config.from_name, self.config.username))
            msg['To'] = to
            msg['Subject'] = subject
            
            if cc:
                msg['Cc'] = ', '.join(cc)
            if bcc:
                msg['Bcc'] = ', '.join(bcc)
            
            # 添加邮件正文
            if is_html:
                msg.attach(MIMEText(body, 'html', 'utf-8'))
            else:
                msg.attach(MIMEText(body, 'plain', 'utf-8'))
            
            # 添加附件
            if attachments:
                for attachment in attachments:
                    self._add_attachment(msg, attachment)
            
            # 准备收件人列表
            recipients = [to]
            if cc:
                recipients.extend(cc)
            if bcc:
                recipients.extend(bcc)
            
            # 发送邮件
            self._send_message(msg, recipients)
            
            logger.info(f"邮件发送成功: {to} - {subject}")
            return True
            
        except Exception as e:
            logger.error(f"邮件发送失败: {e}")
            logger.debug(f"发送详情 - 收件人: {to}, 主题: {subject}", exc_info=True)
            return False
    
    def _add_attachment(self, msg: MIMEMultipart, attachment: Dict[str, Any]):
        """添加附件到邮件"""
        try:
            filename = attachment.get('filename', 'attachment')
            content = attachment.get('content', b'')
            content_type = attachment.get('content_type', 'application/octet-stream')
            
            if isinstance(content, str):
                content = content.encode('utf-8')
            
            part = MIMEBase('application', 'octet-stream')
            part.set_payload(content)
            encoders.encode_base64(part)
            part.add_header(
                'Content-Disposition',
                f'attachment; filename= {filename}'
            )
            msg.attach(part)
            
        except Exception as e:
            logger.warning(f"添加附件失败: {e}")
    
    def _send_message(self, msg: MIMEMultipart, recipients: List[str]):
        """发送邮件消息"""
        # 创建SMTP连接
        if self.config.use_tls:
            # 使用TLS加密
            server = smtplib.SMTP(self.config.host, self.config.port)
            server.starttls(context=ssl.create_default_context())
        else:
            # 使用SSL加密
            context = ssl.create_default_context()
            server = smtplib.SMTP_SSL(self.config.host, self.config.port, context=context)
        
        try:
            # 登录
            server.login(self.config.username, self.config.password)
            
            # 发送邮件
            text = msg.as_string()
            server.sendmail(self.config.username, recipients, text)
            
            logger.debug(f"SMTP发送成功: {len(recipients)}个收件人")
            
        finally:
            server.quit()
    
    def test_connection(self) -> bool:
        """测试SMTP连接"""
        try:
            if self.config.use_tls:
                server = smtplib.SMTP(self.config.host, self.config.port)
                server.starttls(context=ssl.create_default_context())
            else:
                context = ssl.create_default_context()
                server = smtplib.SMTP_SSL(self.config.host, self.config.port, context=context)
            
            server.login(self.config.username, self.config.password)
            server.quit()
            
            logger.info("SMTP连接测试成功")
            return True
            
        except Exception as e:
            logger.error(f"SMTP连接测试失败: {e}")
            return False


def create_smtp_sender(config: Dict[str, Any]) -> Optional[SMTPSender]:
    """
    从配置创建SMTP发送器
    
    Args:
        config: 邮件配置字典
        
    Returns:
        SMTPSender实例或None
    """
    try:
        smtp_config_data = config.get('email', {}).get('smtp', {})
        if not smtp_config_data:
            logger.warning("未找到SMTP配置")
            return None
        
        smtp_config = SMTPConfig(**smtp_config_data)
        return SMTPSender(smtp_config)
        
    except Exception as e:
        logger.error(f"创建SMTP发送器失败: {e}")
        return None


# 全局SMTP发送器实例
_smtp_sender: Optional[SMTPSender] = None


def get_smtp_sender() -> Optional[SMTPSender]:
    """获取全局SMTP发送器实例"""
    return _smtp_sender


def initialize_smtp_sender(config: Dict[str, Any]) -> bool:
    """初始化全局SMTP发送器"""
    global _smtp_sender
    try:
        _smtp_sender = create_smtp_sender(config)
        if _smtp_sender:
            # 测试连接
            if _smtp_sender.test_connection():
                logger.info("SMTP发送器初始化成功")
                return True
            else:
                logger.error("SMTP连接测试失败")
                _smtp_sender = None
                return False
        else:
            logger.warning("SMTP发送器创建失败")
            return False
    except Exception as e:
        logger.error(f"SMTP发送器初始化失败: {e}")
        _smtp_sender = None
        return False


def send_email(to: str, subject: str, body: str, **kwargs) -> bool:
    """
    便捷的邮件发送函数
    
    Args:
        to: 收件人
        subject: 主题
        body: 正文
        **kwargs: 其他参数
        
    Returns:
        bool: 发送是否成功
    """
    sender = get_smtp_sender()
    if not sender:
        logger.error("SMTP发送器未初始化")
        return False
    
    return sender.send_email(to, subject, body, **kwargs)

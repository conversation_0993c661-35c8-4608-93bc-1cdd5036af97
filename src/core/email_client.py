"""
邮件客户端模块 - 负责连接和读取邮件
"""

import imaplib
import poplib
import email
from typing import List, Dict, Any, Union, Optional, cast, Type
from email.message import Message
from email.header import decode_header
from loguru import logger
from .config_manager import EmailConfig


class EmailMessageWrapper:
    """邮件消息类"""

    def __init__(self, raw_message: Message):
        self.raw_message = raw_message
        self._subject = None
        self._sender = None
        self._recipients = None
        self._body = None
        self._attachments = None
        self.uid = None  # 添加UID属性

    @property
    def subject(self) -> str:
        """获取邮件主题"""
        if self._subject is None:
            subject = self.raw_message.get("Subject", "")
            if subject:
                decoded = decode_header(subject)
                self._subject = "".join([
                    text.decode(encoding or 'utf-8') if isinstance(text, bytes) else text
                    for text, encoding in decoded
                ])
            else:
                self._subject = ""
        return self._subject

    @property
    def sender(self) -> str:
        """获取发件人"""
        if self._sender is None:
            self._sender = self.raw_message.get("From", "")
        return self._sender

    @property
    def recipients(self) -> List[str]:
        """获取收件人列表"""
        if self._recipients is None:
            to = self.raw_message.get("To", "")
            cc = self.raw_message.get("Cc", "")
            self._recipients = [addr.strip() for addr in (to + "," + cc).split(",") if addr.strip()]
        return self._recipients

    @property
    def body(self) -> str:
        """获取邮件正文"""
        if self._body is None:
            self._body = self._extract_body()
        return self._body

    @property
    def attachments(self) -> List[Dict[str, Any]]:
        """获取附件信息"""
        if self._attachments is None:
            self._attachments = self._extract_attachments()
        return self._attachments

    def _extract_body(self) -> str:
        """提取邮件正文"""
        body = ""
        try:
            if self.raw_message.is_multipart():
                for part in self.raw_message.walk():
                    content_type = part.get_content_type()
                    if content_type == "text/plain":
                        charset = part.get_content_charset() or 'utf-8'
                        payload = part.get_payload(decode=True)
                        if isinstance(payload, bytes):
                            body = payload.decode(charset, errors='ignore')
                        else:
                            body = str(payload)
                        break
                    elif content_type == "text/html" and not body:
                        charset = part.get_content_charset() or 'utf-8'
                        payload = part.get_payload(decode=True)
                        if isinstance(payload, bytes):
                            body = payload.decode(charset, errors='ignore')
                        else:
                            body = str(payload)
            else:
                charset = self.raw_message.get_content_charset() or 'utf-8'
                payload = self.raw_message.get_payload(decode=True)
                if isinstance(payload, bytes):
                    body = payload.decode(charset, errors='ignore')
                else:
                    body = str(payload)
        except Exception as e:
            logger.error(f"提取邮件正文失败: {e}")
            body = str(self.raw_message.get_payload())

        return body

    def _extract_attachments(self) -> List[Dict[str, Any]]:
        """提取附件信息（包含内容）"""
        attachments = []
        if self.raw_message.is_multipart():
            for part in self.raw_message.walk():
                content_disposition = part.get_content_disposition()
                # 捕获所有类型的附件（attachment, inline 等）
                if content_disposition in ["attachment", "inline"] or part.get_filename():
                    filename = part.get_filename()
                    if filename:
                        # 获取附件内容
                        payload = part.get_payload(decode=True)
                        attachments.append({
                            "filename": filename,
                            "content_type": part.get_content_type(),
                            "size": len(payload),
                            "content": payload,  # 添加内容字段
                            "download_url": ""  # 添加空URL字段
                        })
        return attachments

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "subject": self.subject,
            "sender": self.sender,
            "recipients": self.recipients,
            "body": self.body,
            "attachments": self.attachments,
            "date": self.raw_message.get("Date", ""),
            "message_id": self.raw_message.get("Message-ID", "")
        }


class EmailClient:
    """邮件客户端"""

    def __init__(self, config: EmailConfig):
        self.config = config
        self._connection: Optional[Union[imaplib.IMAP4_SSL, imaplib.IMAP4, poplib.POP3_SSL, poplib.POP3]] = None

    def connect(self) -> bool:
        """连接到邮件服务器"""
        try:
            if self.config.protocol == "imap":
                self._connection = self._connect_imap()
            elif self.config.protocol == "pop3":
                self._connection = self._connect_pop3()
            else:
                raise ValueError(f"不支持的协议: {self.config.protocol}")

            logger.info(f"邮件服务器连接成功: {self.config.host}:{self.config.port}")
            return True

        except Exception as e:
            logger.error(f"邮件服务器连接失败: {e}")
            return False

    def _connect_imap(self) -> Union[imaplib.IMAP4_SSL, imaplib.IMAP4]:
        """连接IMAP服务器"""
        if self.config.use_ssl:
            connection = imaplib.IMAP4_SSL(self.config.host, self.config.port)
        else:
            connection = imaplib.IMAP4(self.config.host, self.config.port)

        connection.login(self.config.username, self.config.password)
        return connection

    def _connect_pop3(self) -> Union[poplib.POP3_SSL, poplib.POP3]:
        """连接POP3服务器"""
        if self.config.use_ssl:
            connection = poplib.POP3_SSL(self.config.host, self.config.port)
        else:
            connection = poplib.POP3(self.config.host, self.config.port)

        connection.user(self.config.username)
        connection.pass_(self.config.password)
        return connection

    def fetch_emails(self, folder: str = "INBOX", limit: int = 10, unread_only: bool = True) -> List[EmailMessageWrapper]:
        """获取邮件列表"""
        if not self._connection:
            if not self.connect():
                return []

        try:
            if self.config.protocol == "imap":
                return self._fetch_emails_imap(folder, limit, unread_only)
            elif self.config.protocol == "pop3":
                return self._fetch_emails_pop3(limit)
            else:
                logger.error(f"不支持的协议: {self.config.protocol}")
                return []
        except Exception as e:
            logger.error(f"获取邮件失败: {e}")
            return []

    def _fetch_emails_imap(self, folder: str, limit: int, unread_only: bool) -> List[EmailMessageWrapper]:
        """通过IMAP获取邮件"""
        if self.config.protocol == "imap" and self._connection is not None:
            # 使用类型断言告诉类型检查器这是IMAP连接
            imap_connection = cast(Union[imaplib.IMAP4_SSL, imaplib.IMAP4], self._connection)
            
            # 支持多个文件夹
            folders = folder.split(',') if ',' in folder else [folder]
            all_emails = []

            for current_folder in folders:
                try:
                    logger.info(f"正在搜索文件夹: {current_folder}")
                    status = imap_connection.select(current_folder)
                    if status[0] != "OK":
                        logger.warning(f"无法选择文件夹 {current_folder}: {status[1]}")
                        continue
                    
                    # 搜索邮件
                    search_criteria = "UNSEEN" if unread_only else "ALL"
                    logger.debug(f"使用搜索条件: {search_criteria}")
                    # 修复SEARCH调用：使用空字符串代替None
                    status, messages = imap_connection.uid('SEARCH', '', search_criteria)
                    logger.debug(f"搜索返回状态: {status}, 消息: {messages}")

                    if status == "OK" and messages and messages[0]:
                        uids = messages[0].split()
                        # 限制邮件数量
                        uids = uids[-limit:] if len(uids) > limit else uids

                        for uid_bytes in uids:
                            try:
                                # 使用UID获取邮件
                                status, msg_data = imap_connection.uid('FETCH', uid_bytes, '(RFC822)')
                                
                                # 确保msg_data有效
                                if status != "OK" or not msg_data:
                                    continue
                                    
                                # 检查消息数据结构
                                if isinstance(msg_data[0], tuple) and len(msg_data[0]) >= 2:
                                    # 确保msg_data[0][1]存在并且是bytes类型
                                    raw_email_bytes = msg_data[0][1]
                                    if isinstance(raw_email_bytes, bytes):
                                        # 使用标准方法解析邮件
                                        raw_email = email.message_from_bytes(raw_email_bytes)
                                        email_message = EmailMessageWrapper(raw_email)
                                        # 使用安全的UID解码
                                        uid_str = uid_bytes.decode('utf-8')
                                        email_message.uid = uid_str
                                        all_emails.append(email_message)
                            except Exception as e:
                                # 安全处理UID解码
                                try:
                                    uid_str = uid_bytes.decode('utf-8') if uid_bytes else "unknown"
                                except Exception as decode_error:
                                    uid_str = f"invalid_uid({decode_error})"
                                logger.error(f"处理邮件 UID {uid_str} 失败: {e}")
                except Exception as e:
                    logger.error(f"处理文件夹 {current_folder} 失败: {e}")
            
            return all_emails
        
        logger.warning("IMAP连接不可用")
        return []

    def mark_as_read(self, uid: str, folder: str = "INBOX"):
        """使用UID标记邮件为已读（仅IMAP协议支持）"""
        if not self._connection or self.config.protocol != "imap":
            logger.warning("无法标记邮件为已读：未连接或协议不支持")
            return

        try:
            imap_connection = cast(Union[imaplib.IMAP4_SSL, imaplib.IMAP4], self._connection)
            imap_connection.select(folder)
            
            # 使用UID命令来标记邮件为已读
            result = imap_connection.uid('STORE', uid, '+FLAGS', '\\Seen')
            
            if result[0] == 'OK':
                logger.info(f"邮件标记为已读: UID {uid}")
            else:
                logger.error(f"标记邮件失败: {result[1][0].decode('utf-8')}")
        except imaplib.IMAP4.error as e:
            logger.error(f"IMAP协议错误: {e}")
        except Exception as e:
            logger.error(f"标记邮件为已读时发生未知错误: {e}")

    def _fetch_emails_pop3(self, limit: int) -> List[EmailMessageWrapper]:
        """通过POP3获取邮件"""
        emails = []

        if self.config.protocol == "pop3" and self._connection is not None:
            # 使用类型断言告诉类型检查器这是POP3连接
            pop3_connection = cast(Union[poplib.POP3_SSL, poplib.POP3], self._connection)
            
            try:
                num_messages = len(pop3_connection.list()[1])
                start = max(1, num_messages - limit + 1)

                for i in range(start, num_messages + 1):
                    try:
                        raw_email_lines = pop3_connection.retr(i)[1]
                        raw_email = email.message_from_bytes(b'\n'.join(raw_email_lines))
                        emails.append(EmailMessageWrapper(raw_email))
                    except Exception as e:
                        logger.error(f"获取邮件 {i} 失败: {e}")
            except Exception as e:
                logger.error(f"POP3获取邮件列表失败: {e}")

        return emails

    def disconnect(self):
        """断开连接"""
        if self._connection:
            try:
                if self.config.protocol == "imap":
                    # 使用类型断言告诉类型检查器这是IMAP连接
                    imap_connection = cast(Union[imaplib.IMAP4_SSL, imaplib.IMAP4], self._connection)
                    imap_connection.logout()
                elif self.config.protocol == "pop3":
                    # 使用类型断言告诉类型检查器这是POP3连接
                    pop3_connection = cast(Union[poplib.POP3_SSL, poplib.POP3], self._connection)
                    pop3_connection.quit()
                logger.info("邮件服务器连接已断开")
            except Exception as e:
                logger.error(f"断开连接时出错: {e}")
            finally:
                self._connection = None

    def __enter__(self):
        self.connect()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.disconnect()

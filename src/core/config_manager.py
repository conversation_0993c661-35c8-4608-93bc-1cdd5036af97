"""
配置管理模块 - 负责加载和管理应用配置
"""

import os
import yaml
from typing import Dict, Any, Optional
from pydantic import BaseModel, Field, validator
from pydantic_settings import BaseSettings
from loguru import logger


class EmailConfig(BaseModel):
    """邮箱配置"""
    host: str = Field(..., description="邮箱服务器地址")
    port: int = Field(993, description="端口号")
    username: str = Field(..., description="用户名")
    password: str = Field(..., description="密码")
    use_ssl: bool = Field(True, description="是否使用SSL")
    protocol: str = Field("imap", description="协议类型: imap/pop3")

    @validator('protocol')
    def validate_protocol(cls, v):
        if v.lower() not in ['imap', 'pop3']:
            raise ValueError('协议必须是 imap 或 pop3')
        return v.lower()


class AIConfig(BaseModel):
    """AI配置"""
    provider: str = Field("openai", description="AI提供商: openai/anthropic")
    api_key: str = Field(..., description="API密钥")
    model: str = Field("gpt-3.5-turbo", description="模型名称")
    base_url: Optional[str] = Field(None, description="自定义API地址")
    max_tokens: int = Field(1000, description="最大token数")
    temperature: float = Field(0.7, description="温度参数")


class SchedulerConfig(BaseModel):
    """调度器配置"""
    check_interval: int = Field(300, description="检查间隔(秒)")
    max_emails_per_check: int = Field(50, description="每次检查最大邮件数")
    enable_scheduler: bool = Field(True, description="是否启用调度器")


class LogConfig(BaseModel):
    """日志配置"""
    level: str = Field("INFO", description="日志级别")
    file_path: str = Field("logs/mailer.log", description="日志文件路径")
    max_size: str = Field("10 MB", description="单个日志文件最大大小")
    retention: str = Field("30 days", description="日志保留时间")


class AppConfig(BaseSettings):
    """应用主配置"""
    email: EmailConfig
    ai: AIConfig
    scheduler: SchedulerConfig = SchedulerConfig(check_interval=300, max_emails_per_check=50, enable_scheduler=True)
    log: LogConfig = LogConfig(level="INFO", file_path="logs/mailer.log", max_size="10 MB", retention="30 days")

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        extra = "allow"  # 允许额外字段


class ConfigManager:
    """配置管理器"""

    def __init__(self, config_path: str = "config/config.yaml"):
        self.config_path = config_path
        self._config: Optional[AppConfig] = None

    def load_config(self) -> AppConfig:
        """加载配置文件"""
        try:
            if not os.path.exists(self.config_path):
                raise FileNotFoundError(f"配置文件不存在: {self.config_path}")

            with open(self.config_path, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)

            self._config = AppConfig(**config_data)
            logger.info(f"配置文件加载成功: {self.config_path}")
            return self._config

        except Exception as e:
            logger.error(f"配置文件加载失败: {e}")
            raise

    @property
    def config(self) -> AppConfig:
        """获取配置对象"""
        if self._config is None:
            self._config = self.load_config()
        return self._config

    def reload_config(self) -> AppConfig:
        """重新加载配置"""
        self._config = None
        return self.load_config()

    def validate_config(self) -> bool:
        """验证配置有效性"""
        try:
            config = self.config
            # 这里可以添加更多的验证逻辑
            logger.info("配置验证通过")
            return True
        except Exception as e:
            logger.error(f"配置验证失败: {e}")
            return False
            
    @staticmethod
    def get_email_config() -> Dict[str, Any]:
        """获取邮件配置"""
        # 简化实现，实际应从配置文件读取
        return {
            "smtp_server": "smtp.example.com",
            "smtp_port": 587,
            "username": "<EMAIL>",
            "password": "password"
        }
